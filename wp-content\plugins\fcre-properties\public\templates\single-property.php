<?php
get_header();
global $post;
$post_id = $post->ID;
the_post();
$FCRE = Fcre_Global::getInstance();
$image = get_the_post_thumbnail_url($post_id, 'full');
$gallery_photos = get_post_meta($post->ID, 'photo_gallery', true);
$address = get_post_meta($post->ID, 'address', true);
$site = get_post_meta($post->ID, 'site', true);
$building = get_post_meta($post->ID, 'building', true);
$building_size = get_post_meta($post->ID, 'building_size', true);
$building_class = get_post_meta($post->ID, 'building_class', true);
$lease_rate = get_post_meta($post->ID, 'lease_rate', true);
$year_built = get_post_meta($post->ID, 'year_built', true);
$latitude = get_post_meta($post->ID, 'latitude', true);
$longitude = get_post_meta($post->ID, 'longitude', true);
$property_flyer = get_post_meta($post->ID, 'property_flyer', true);
$property_om = get_post_meta($post->ID, 'property_om', true);
$documents_gallery = get_post_meta($post->ID, 'documents_gallery', true);

$ca_check = get_post_meta($post->ID, 'ca_check', true);
$confidentiality_agreement_text = get_post_meta($post->ID, 'confidential_agreement', true);
$flyer_ca_required = get_post_meta($post->ID, 'flyer_ca_required', true);
$om_ca_required = get_post_meta($post->ID, 'om_ca_required', true);

$related_agents = get_post_meta($post->ID, 'related_agents', true);
$related_agents = array_filter(explode(',', $related_agents));

$agent_emails = [];

if($related_agents){
    foreach($related_agents as $agent_id){
        $agent_email = get_post_meta($agent_id, 'email', true);
        if($agent_email){
            $agent_emails[] = $agent_email;
        }
    }
}

$property_type = fcre_get_option_label_from_meta($post->ID, 'property_types', $FCRE->plugin_name . '-property-types');
$transaction_type = fcre_get_option_label_from_meta($post->ID, 'transaction_types', $FCRE->plugin_name . '-transaction-types');
$property_status = fcre_get_option_label_from_meta($post->ID, 'status', $FCRE->plugin_name . '-property-status');

?>


<main class="fcre-single-property">
    <div class="fcre-container">
        <h1><?php the_title(); ?></h1>
        <p><?= $address ?></h>
        <div class="custom-tabs">

            <div class="fcre-tab-buttons">
                <button class="fcre-icon-btn fcre-tab-btn active" data-tab="overview"><i class="fas fa-info-circle"></i> Overview</button>
                <?php if ($ca_check == 'y' && !isset($_COOKIE['agreement_submit'])) { ?>
                <button class="fcre-icon-btn fcre-tab-tooltip custom-modal-trigger" data-modal-id="fcre-ca-modal"><i class="fas fa-lock"></i> Documents <span class="tooltip-text">Sign the Agreement to Unlock</span></button>
                <?php }else{ ?>
                <button class="fcre-icon-btn fcre-tab-btn" data-tab="documents"><i class="fas fa-file-alt"></i> Documents</button>
                <?php } ?>
                <button class="fcre-icon-btn fcre-tab-btn" data-tab="photos"><i class="fas fa-image"></i>Photos</button>
                <button class="fcre-icon-btn fcre-tab-btn" data-tab="map-tab"><i class="fas fa-map-marked-alt"></i> Map</button>
                <button class="fcre-icon-btn fcre-tab-btn" data-tab="demographics"><i class="fas fa-users"></i> Demographics</button>
            </div>

            <div class="fcre-tab-content active" id="overview">
                <div class="fcre-row">
                    <div class="fcre-col fcre-col-8">

                        <div class="fotorama" data-max-width="100%" data-loop="true" data-autoplay="true" data-nav="thumbs" data-allowfullscreen="true" data-keyboard="true" data-fit="cover">
                            <?php
                            if (!empty($gallery_photos)) {
                                foreach ($gallery_photos as $i => $photo) {
                                    $photo_large_url = wp_get_attachment_image_url($photo['id'], 'full');
                                    $photo_thumb_url = wp_get_attachment_image_url($photo['id'], 'thumbnail');
                                    $caption = isset($photo['caption']) ? $photo['caption'] : '';
                            ?>
                                    <a href="<?php echo esc_url($photo_large_url); ?>">
                                        <img class="img-fluid" src="<?php echo esc_url($photo_large_url); ?>" alt="<?php the_title(); ?>" />
                                    </a>
                            <?php
                                }
                            }
                            ?>
                        </div>

                        <div class="fcre-single-property-content">
                            <div class="fcre-single-property-overview">
                                <h3 class="fcre-single-property-title">Property Details</h3>
                                <div class="fcre-single-property-overview-item">
                                    <span class="fcre-single-property-overview-item-label">Property Type:</span>
                                    <span class="fcre-single-property-overview-item-value">
                                        <?php echo $property_type ?>
                                    </span>
                                </div>
                                <div class="fcre-single-property-overview-item">
                                    <span class="fcre-single-property-overview-item-label">Transaction Type:</span>
                                    <span class="fcre-single-property-overview-item-value">
                                        <?php echo $transaction_type; ?>
                                    </span>
                                </div>
                                <div class="fcre-single-property-overview-item">
                                    <span class="fcre-single-property-overview-item-label">Address:</span>
                                    <span class="fcre-single-property-overview-item-value"><?= $address ?></span>
                                </div>
                                <div class="fcre-single-property-overview-item">
                                    <span class="fcre-single-property-overview-item-label">Site:</span>
                                    <span class="fcre-single-property-overview-item-value"><?= $site ?></span>
                                </div>
                                <div class="fcre-single-property-overview-item">
                                    <span class="fcre-single-property-overview-item-label">Building:</span>
                                    <span class="fcre-single-property-overview-item-value"><?= $building ?></span>
                                </div>
                                <div class="fcre-single-property-overview-item">
                                    <span class="fcre-single-property-overview-item-label">Building Size:</span>
                                    <span class="fcre-single-property-overview-item-value"><?= $building_size ?></span>
                                </div>
                                <div class="fcre-single-property-overview-item">
                                    <span class="fcre-single-property-overview-item-label">Building Class:</span>
                                    <span class="fcre-single-property-overview-item-value"><?= $building_class ?></span>
                                </div>
                                <div class="fcre-single-property-overview-item">
                                    <span class="fcre-single-property-overview-item-label">Lease Rate:</span>
                                    <span class="fcre-single-property-overview-item-value"><?= $lease_rate ?></span>
                                </div>
                                <div class="fcre-single-property-overview-item">
                                    <span class="fcre-single-property-overview-item-label">Year Built:</span>
                                    <span class="fcre-single-property-overview-item-value"><?= $year_built ?></span>
                                </div>
                                <div class="fcre-single-property-overview-item">
                                    <span class="fcre-single-property-overview-item-label">Property Status:</span>
                                    <span class="fcre-single-property-overview-item-value"><?= $property_status ?></span>
                                </div>
                            </div>
                            <div class="fcre-single-property-description">
                                <h3 class="fcre-single-property-title">Property Overview</h3>
                                <?php the_content(); ?>
                            </div>
                        </div>
                        
                    </div>
                    <div class="fcre-col fcre-col-4">
                        <div class="fcre-single-property-sidebar">
                            <?php if ($property_flyer) {
                                // Check if flyer requires confidentiality agreement
                                $flyer_disabled = ($flyer_ca_required == 'y' && !isset($_COOKIE['agreement_submit']));
                                $flyer_class = $flyer_disabled ? 'fcre-btn fcre-btn-primary fcre-icon-btn fcre-btn-download-flyer fcre-btn-disabled' : 'fcre-btn fcre-btn-primary fcre-icon-btn fcre-btn-download-flyer';
                                $flyer_href = $flyer_disabled ? '#' : $property_flyer;
                                $flyer_title = $flyer_disabled ? 'Please sign the confidentiality agreement to download this flyer' : '';
                            ?>
                                <a href="<?php echo $flyer_href; ?>" class="<?php echo $flyer_class; ?>" title="<?php echo $flyer_title; ?>"><i class="fas fa-file"></i> Download Flyer</a>
                            <?php } ?>

                            <?php if ($property_om) {
                                // Check if OM requires confidentiality agreement
                                $om_disabled = ($om_ca_required == 'y' && !isset($_COOKIE['agreement_submit']));
                                $om_class = $om_disabled ? 'fcre-btn fcre-btn-primary fcre-icon-btn fcre-btn-download-om fcre-btn-disabled' : 'fcre-btn fcre-btn-primary fcre-icon-btn fcre-btn-download-om';
                                $om_href = $om_disabled ? '#' : $property_om;
                                $om_title = $om_disabled ? 'Please sign the confidentiality agreement to download this offering memorandum' : '';
                            ?>
                                <a href="<?php echo $om_href; ?>" class="<?php echo $om_class; ?>" title="<?php echo $om_title; ?>"><i class="fas fa-file"></i> Download Offering Memorandum</a>
                            <?php }  ?>


                            <?php if ($ca_check == 'y' && !isset($_COOKIE['agreement_submit'])) { ?>
                                <button class="custom-modal-trigger fcre-btn fcre-btn-primary fcre-btn-agreement" data-modal-id="fcre-ca-modal">Confidentiality Agreement</button>
                            <?php } ?>

                            <?php
                                    if($related_agents){ ?>
                            <div class="fcre-single-property-sidebar-agents">
                                <h4 class="fcre-single-property-sidebar-title">Listing Agents</h4>
                                <ul class="fcre-agent-list">
                                    <?php
                                        foreach($related_agents as $agent_id){
                                            $agent_name = get_the_title($agent_id);
                                            $agent_email = get_post_meta($agent_id, 'email', true);
                                            $agent_phone = get_post_meta($agent_id, 'phone', true);
                                            $agent_photo =  get_post_thumbnail_id($agent_id);
                                            $agent_photo_url = wp_get_attachment_image_url($agent_photo, 'thumbnail');
                                    ?>
                                            <li class="fcre-agent-item">
                                                <div class="fcre-agent-photo">
                                                    <img src="<?php echo esc_url($agent_photo_url); ?>" alt="<?php echo esc_attr($agent_name); ?>" />
                                                </div>
                                                <div class="fcre-agent-info">
                                                    <h3><?php echo esc_html($agent_name); ?></h3>
                                                    <p> <a href="mailto:<?php echo esc_attr($agent_email); ?>"><?php echo esc_html($agent_email); ?></a> </p>
                                                    <p><a href="tel:<?php echo esc_attr($agent_phone); ?>" > <?php echo esc_html($agent_phone); ?></a></p>
                                                </div>
                                            </li>
                                    <?php
                                        }
                                    
                                    ?>
                                </ul>
                            </div>
                            <?php
                                   }
                               ?>

                            </div>

                            <div class="fcre-single-property-request-more-info">
                                <h4 class="fcre-single-property-sidebar-title">Request More Info</h4>
                                <form method="post" id="request-more-info-form">
                                    <input type="hidden" name="action" value="fcre_submit_request_more_info">
                                    <input type="hidden" name="agent_emails" value="<?php echo fcre_get_agent_emails($post->ID); ?>">
                                    <input type="hidden" name="property_id" value="<?php echo $post->ID; ?>">

                                    <div class="fcre-form-group">
                                        <input type="text" class="fcre-form-control" id="name" name="name" placeholder="Name">
                                    </div>

                                    <div class="fcre-form-group">
                                        <input type="email" class="fcre-form-control" id="email" name="email" placeholder="Email">
                                    </div>
                                    <div class="fcre-form-group">
                                        <input type="tel" class="fcre-form-control" id="phone" name="phone" pattern="[0-9]{3}-[0-9]{3}-[0-9]{4}" placeholder="Phone">
                                    </div>
                                    <div class="fcre-form-group">
                                        <textarea class="fcre-form-control" id="message" name="message" rows="4" placeholder="Enter your message here..."></textarea>
                                    </div>

                                    <button type="submit" class="fcre-btn fcre-btn-primary w-full">Submit</button>

                                </form>
                            </div>
                        </div>
                </div>

                <div class="fcre-single-property-map-overview">
                    <div id="fcre-single-property-map-overview" class="fcre-single-property-map-overview" data-lat="<?= $latitude ?>" data-lng="<?= $longitude ?>" data-address="<?= $address ?>"></div>
                </div>
            </div>
            <div class="fcre-tab-content" id="documents">
                <ul>
                    <?php

                    if ($documents_gallery) {
                        foreach ($documents_gallery as $i => $file) {
                            $file_url = wp_get_attachment_url($file['file']);
                            $file_name = basename(get_attached_file($file['file']));
                            $file_type = wp_check_filetype($file_url);
                    ?>
                            <li><a href="<?php echo esc_url($file_url); ?>" class="fcre-document-link <?=$file_type['ext'];?>" target="_blank"><?php echo esc_html($file_name); ?></a></li>
                    <?php
                        }
                    }

                    ?>
                </ul>
                
            </div>
            <div class="fcre-tab-content" id="photos">
                <div class="fcre-photos-gallery">
                <div class="fotorama" data-max-width="100%" data-loop="true" data-autoplay="true" data-nav="thumbs" data-allowfullscreen="true" data-keyboard="true" data-fit="cover">
                    <?php
                    if (!empty($gallery_photos)) {
                        foreach ($gallery_photos as $i => $photo) {
                            $photo_large_url = wp_get_attachment_image_url($photo['id'], 'full');
                            $photo_thumb_url = wp_get_attachment_image_url($photo['id'], 'thumbnail');
                            $caption = isset($photo['caption']) ? $photo['caption'] : '';
                    ?>
                            <a href="<?php echo esc_url($photo_large_url); ?>">
                                <img class="img-fluid" src="<?php echo esc_url($photo_large_url); ?>" alt="<?php the_title(); ?>" />
                            </a>
                    <?php
                        }
                    }
                    ?>
                </div>
             </div>


            </div>
            <div class="fcre-tab-content" id="demographics">
                <div class="fcre-row">
                
                <?php
                $demographics = get_post_meta($post->ID, 'demographics', true);
                if ($demographics) {
                    foreach ($demographics as $i => $demographic) {
                ?>
                    <div class="fcre-col-4">
                        <div class="fcre-single-property-demographics">
                            <div class="fcre-single-property-demographics-item">
                                <h2 class="fcre-single-property-title"><?php echo esc_html($demographic['title']); ?></h2>
                                <p>Population: <?php echo esc_html($demographic['population']); ?></p>
                                <p> Average Income: <?php echo esc_html($demographic['average_income']); ?></p>
                            </div>
                        </div>
                    </div>    
                <?php
                    }
                }
                ?>
                </div>

            </div>
            <div class="fcre-tab-content" id="map-tab">
                <div class="fcre-single-property-map-tab">
                    <div id="fcre-single-property-map" class="fcre-single-property-map" data-lat="<?= $latitude ?>" data-lng="<?= $longitude ?>" data-address="<?= $address ?>"></div>
                </div>
            </div>
        </div>

    </div>
</main>

<div id="fcre-ca-modal" class="custom-modal">
    <div class="custom-modal-content">
        <span class="custom-modal-close">&times;</span>
        <h2>Confidentiality Agreement</h2>
        <p><?= $confidentiality_agreement_text ?></p>

        <form method="post" id="agreement-from">

            <input type="hidden" name="action" value="fcre_submit_agreement">

            <input type="hidden" name="agent_emails" value="<?php echo fcre_get_agent_emails($post->ID); ?>">

            <input type="hidden" name="property_id" value="<?php echo $post->ID; ?>">

            <div class="fcre-form-group">
                <label for="email">E-mail:</label>
                <input type="email" class="fcre-form-control" id="agreement-email" data-property-id="<?php echo $post->ID; ?>" name="email">
                <div class="agreement-message" style="display: none;">You have already signed the agreement for this property. <a href="<?php echo get_permalink($post->ID);?>">Click Here</a> to view the documents.</div>
            </div>

            <div class="fcre-form-group">
                <label for="individual_name">Name:</label>
                <input type="text" class="fcre-form-control" id="name" name="name">
            </div>
            
            <div class="fcre-form-group">
                <label for="phone">Phone:</label>
                <input type="tel" class="fcre-form-control" id="phone" name="phone" pattern="[0-9]{3}-[0-9]{3}-[0-9]{4}" placeholder="************">
            </div>

            <div class="fcre-form-group">
                <label for="date">Date:</label>
                <input type="text" class="fcre-form-control" id="date" name="date">
            </div>

            <div class="fcre-form-group">
                <label for="address">Address:</label>
                <input type="text" class="fcre-form-control" id="address" name="address">
            </div>

            <div class="fcre-form-group">
                <label for="city_state_zip">City, State Zip:</label>
                <input type="text" class="fcre-form-control" id="city_state_zip" name="city_state_zip">
            </div>
            
            <button id="agreement-submit" type="submit" class="fcre-btn fcre-btn-primary">Submit</button>
            
        </form>
    </div>
</div>


<?php get_footer(); ?>