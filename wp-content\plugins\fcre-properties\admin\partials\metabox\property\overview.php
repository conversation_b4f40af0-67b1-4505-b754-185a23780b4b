<?php
 
$address = get_post_meta($post->ID, 'address', true);
$site = get_post_meta($post->ID, 'site', true);
$building = get_post_meta($post->ID, 'building', true);
$building_size = get_post_meta($post->ID, 'building_size', true);
$building_class = get_post_meta($post->ID, 'building_class', true);
$lease_rate = get_post_meta($post->ID, 'lease_rate', true);
$year_built = get_post_meta($post->ID, 'year_built', true);
$property_status = get_post_meta($post->ID, 'status', true);
$latitude = get_post_meta($post->ID, 'latitude', true);
$longitude = get_post_meta($post->ID, 'longitude', true);

?>
<div class="fcre-container">
<div class="fcre-row">
    <div class="fcre-col fcre-col-4">
    <div class="fcre-form-group">
    <?php
        fcre_dynamic_multiselect($post, [
            'option_key'  => $this->FCRE->plugin_name . '-transaction-types',
            'meta_key'    => 'transaction_types',
            'field_name'  => 'transaction_types',
            'label'       => 'Transaction Types',
            'placeholder' => 'Select Transaction Types',
        ]);
    ?>
    </div>
    </div>
    <div class="fcre-col fcre-col-4">
    <div class="fcre-form-group">
        <?php
            fcre_dynamic_multiselect($post, [
                'option_key'  => $this->FCRE->plugin_name . '-property-types',
                'meta_key'    => 'property_types',
                'field_name'  => 'property_types',
                'label'       => 'Property Types',
                'placeholder' => 'Select Property Types',
            ]);
        ?>
    </div>
    </div>
    <div class="fcre-col fcre-col-4">
    <div class="fcre-form-group">
        <?php
            fcre_dynamic_select($post, [
                'option_key'  => $this->FCRE->plugin_name . '-property-status',
                'meta_key'    => 'status',
                'field_name'  => 'status',
                'label'       => 'Property Status',
                'placeholder' => 'Select Property Status',
            ]);
        ?>
    </div>
    </div>
</div>

<!-- Row 1 -->
<div class="fcre-row">
            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="address"><?php _e('Address', 'textdomain'); ?></label>
                    <input type="text" id="address" name="address" value="<?php echo esc_attr($address); ?>" />
                </div>
            </div>
            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="site"><?php _e('Site', 'textdomain'); ?></label>
                    <input type="text" id="site" name="site" value="<?php echo esc_attr($site); ?>" />
                </div>
            </div>
            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="building"><?php _e('Building', 'textdomain'); ?></label>
                    <input type="text" id="building" name="building" value="<?php echo esc_attr($building); ?>" />
                </div>
            </div>

            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="building_size"><?php _e('Building Size', 'textdomain'); ?></label>
                    <input type="number" id="building_size" name="building_size" value="<?php echo esc_attr($building_size); ?>" />
                </div>
            </div>
            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="building_class"><?php _e('Building Class', 'textdomain'); ?></label>
                    <input type="text" id="building_class" name="building_class" value="<?php echo esc_attr($building_class); ?>" />
                </div>
            </div>
            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="lease_rate"><?php _e('Lease Rate', 'textdomain'); ?></label>
                    <input type="text" id="lease_rate" name="lease_rate" value="<?php echo esc_attr($lease_rate); ?>" />
                </div>
            </div>

            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="year_built"><?php _e('Year Built', 'textdomain'); ?></label>
                    <input type="number" id="year_built" name="year_built" value="<?php echo esc_attr($year_built); ?>" />
                </div>
            </div>

            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="latitude"><?php _e('Latitude', 'textdomain'); ?></label>
                    <input type="text" id="latitude" name="latitude" value="<?php echo esc_attr($latitude); ?>" />
                </div>
            </div>

            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="longitude"><?php _e('Longitude', 'textdomain'); ?></label>
                    <input type="text" id="longitude" name="longitude" value="<?php echo esc_attr($longitude); ?>" />
                </div>
            </div>
            <div class="fcre-col fcre-col-4">
                 <div class="fcre-form-group">
                <?php
                fcre_file_upload_field($post,[
                    'meta_key'    => 'property_flyer',
                    'field_name'  => 'property_flyer',
                    'label'       => 'Upload Flyer',
                    'placeholder' => 'Upload Flyer',
                    'group_class' => 'mb-3',
                ]);
                ?>
                 </div>
            </div>
            <div class="fcre-col fcre-col-4">
                 <div class="fcre-form-group">
                <?php
                fcre_file_upload_field($post,[
                    'meta_key'    => 'property_om',
                    'field_name'  => 'property_om',
                    'label'       => 'Upload Offering Memorandum',
                    'placeholder' => 'Upload Offering Memorandum',
                    'group_class' => 'mb-3',
                ]);
                ?>
                 </div>
            </div>
        </div>

        <div class="fcre-row">
            <div class="fcre-col fcre-col-6">
                <div class="fcre-form-group">
                <?php
                        fcre_wysiwyg_field($post, [
                    'meta_key'    => 'virtual_tour',
                    'field_name'  => 'virtual_tour',
                    'label'       => 'Virtual Tour',
                    'group_class' => 'mb-4',
                    'editor_args' => [
                        'media_buttons' => false, // enable media uploader
                        'teeny'         => true // show full editor
                    ]
                ]);
                ?>
                </div>
            </div>
            <div class="fcre-col fcre-col-6">
                <div class="fcre-form-group">
                <?php
                        fcre_wysiwyg_field($post, [
                    'meta_key'    => 'property_video',
                    'field_name'  => 'property_video',
                    'label'       => 'Property Video',
                    'group_class' => 'mb-4',
                    'editor_args' => [
                        'media_buttons' => false, // enable media uploader
                        'teeny'         => true // show full editor
                    ]
                ]);
                ?>
                </div>
            </div>
        </div>
         
</div>

